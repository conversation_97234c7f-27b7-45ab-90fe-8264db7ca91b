@echo off
echo ========================================
echo 使用已安装的Java运行Spring Boot项目
echo ========================================

set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo 检查Java环境...
"%JAVA_HOME%\bin\java.exe" -version
if %errorLevel% neq 0 (
    echo Java检查失败
    pause
    exit /b 1
)

echo.
echo 检查项目文件...
if not exist "pom.xml" (
    echo 错误：未找到pom.xml文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo.
echo 尝试下载Maven...
if not exist "maven" (
    echo 正在下载Maven...
    powershell -Command "Invoke-WebRequest -Uri 'https://archive.apache.org/dist/maven/maven-3/3.8.8/binaries/apache-maven-3.8.8-bin.zip' -OutFile 'maven.zip'"
    if %errorLevel% == 0 (
        echo 正在解压Maven...
        powershell -Command "Expand-Archive -Path 'maven.zip' -DestinationPath '.'"
        ren apache-maven-3.8.8 maven
        del maven.zip
        echo Maven下载完成
    ) else (
        echo Maven下载失败，尝试使用Java直接编译
        goto java_compile
    )
)

echo.
echo 设置Maven环境...
set MAVEN_HOME=%CD%\maven
set PATH=%MAVEN_HOME%\bin;%PATH%

echo 检查Maven...
"%MAVEN_HOME%\bin\mvn.cmd" -version
if %errorLevel% neq 0 (
    echo Maven检查失败，尝试使用Java直接编译
    goto java_compile
)

echo.
echo 使用Maven编译和运行项目...
"%MAVEN_HOME%\bin\mvn.cmd" clean compile
if %errorLevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 启动Spring Boot应用...
"%MAVEN_HOME%\bin\mvn.cmd" spring-boot:run
goto end

:java_compile
echo.
echo ========================================
echo 使用Java直接编译（备用方案）
echo ========================================
echo 注意：这是简化的编译方式，可能无法处理所有依赖

echo 创建编译目录...
if not exist "target\classes" mkdir target\classes

echo 编译Java源文件...
"%JAVA_HOME%\bin\javac.exe" -d target\classes -cp "src\main\java" src\main\java\com\spzx\admin\*.java src\main\java\com\spzx\admin\*\*.java src\main\java\com\spzx\admin\*\*\*.java

if %errorLevel% neq 0 (
    echo 编译失败 - 需要Maven来处理依赖
    echo.
    echo 建议：
    echo 1. 手动安装Maven
    echo 2. 或者以管理员身份运行安装脚本
    pause
    exit /b 1
)

echo 编译成功，但无法运行Spring Boot应用（需要Maven）

:end
pause
