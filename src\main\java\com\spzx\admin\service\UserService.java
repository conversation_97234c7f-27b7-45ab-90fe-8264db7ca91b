package com.spzx.admin.service;

import com.spzx.admin.dto.LoginRequest;
import com.spzx.admin.dto.LoginResponse;
import com.spzx.admin.dto.UserInfoResponse;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface UserService {
    
    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest loginRequest);
    
    /**
     * 获取用户信息
     */
    UserInfoResponse getUserInfo(String userName);
    
    /**
     * 刷新token
     */
    LoginResponse refreshToken(String refreshToken);
}
