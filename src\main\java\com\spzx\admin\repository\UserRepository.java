package com.spzx.admin.repository;

import com.spzx.admin.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUserName(String userName);
    
    /**
     * 根据用户名和状态查找用户
     */
    Optional<User> findByUserNameAndStatus(String userName, Integer status);
}
