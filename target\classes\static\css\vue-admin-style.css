/* Vue3 Element Admin 风格样式 */

/* 重置登录页面样式 */
.login-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.login-wrapper {
    position: relative;
    z-index: 1;
}

.login-box {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 420px;
    padding: 50px 40px;
}

.login-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: -0.5px;
    margin-bottom: 40px;
}

/* 输入框样式 */
.input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.input-wrapper input {
    width: 100%;
    padding: 15px 45px 15px 45px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s;
    background: #fff;
}

.input-wrapper input:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #c0c4cc;
    z-index: 2;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #c0c4cc;
    cursor: pointer;
    z-index: 2;
}

/* 验证码样式 */
.captcha-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.captcha-input {
    flex: 1;
    margin-bottom: 0;
}

.captcha-image {
    width: 120px;
    height: 40px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
}

/* 记住密码 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid #dcdfe6;
    border-radius: 3px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: #409eff;
    border-color: #409eff;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 12px;
    top: -2px;
    left: 2px;
}

/* 登录按钮 */
.login-btn {
    background: #409eff;
    border: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s;
}

.login-btn:hover {
    background: #66b1ff;
    transform: none;
}

/* 主界面头部 */
.header {
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-icon {
    font-size: 24px;
    color: #409eff;
}

.logo-text {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: #606266;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s;
}

.menu-toggle:hover {
    background: #f5f7fa;
}

/* 面包屑导航 */
.header-center {
    flex: 1;
    margin-left: 20px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.breadcrumb-item {
    color: #606266;
}

.breadcrumb-item.active {
    color: #303133;
    font-weight: 500;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: #c0c4cc;
}

/* 头部右侧 */
.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #606266;
    transition: all 0.3s;
}

.action-btn:hover {
    background: #f5f7fa;
    color: #409eff;
}

/* 用户信息 */
.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s;
    position: relative;
}

.user-info:hover {
    background: #f5f7fa;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #409eff;
}

.dropdown-arrow {
    font-size: 12px;
    color: #c0c4cc;
    transition: transform 0.3s;
}

.user-info:hover .dropdown-arrow {
    transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.user-info:hover .user-dropdown {
    display: block;
}

.dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #606266;
    font-size: 14px;
    transition: all 0.3s;
}

.dropdown-item:hover {
    background: #f5f7fa;
    color: #409eff;
}

.dropdown-divider {
    height: 1px;
    background: #e4e7ed;
    margin: 6px 0;
}

/* 侧边栏样式 */
.sidebar {
    background: #304156;
    width: 210px;
}

.nav-menu {
    padding: 0;
}

.nav-item {
    color: #bfcbd9;
    border-left: none;
    padding: 0;
    margin: 0;
}

.nav-item:hover {
    background: #263445;
    border-left: none;
}

.nav-item.active {
    background: #409eff;
    color: white;
}

.nav-item > i,
.nav-item > span {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 导航组 */
.nav-group {
    color: #bfcbd9;
}

.nav-group-title {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.nav-group-title:hover {
    background: #263445;
}

.group-arrow {
    margin-left: auto;
    font-size: 12px;
    transition: transform 0.3s;
}

.nav-group.expanded .group-arrow {
    transform: rotate(180deg);
}

.nav-group-items {
    background: #1f2d3d;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s;
}

.nav-group.expanded .nav-group-items {
    max-height: 200px;
}

.nav-group-items .nav-item {
    padding-left: 50px;
}

.nav-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #bfcbd9;
    margin-right: 12px;
}

/* 主内容区调整 */
.main-container {
    grid-template-rows: 60px 1fr;
    grid-template-columns: 210px 1fr;
}

.main-content {
    background: #f0f2f5;
    padding: 20px;
}

/* 页面头部样式 */
.page-header {
    background: white;
    padding: 16px 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title h2 {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
    margin: 0;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    width: 120px;
}

.search-input:focus {
    border-color: #409eff;
    outline: none;
}

/* 按钮样式更新 */
.btn {
    padding: 9px 15px;
    font-size: 14px;
    border-radius: 4px;
    border: 1px solid transparent;
}

.btn-primary {
    background: #409eff;
    border-color: #409eff;
}

.btn-primary:hover {
    background: #66b1ff;
    border-color: #66b1ff;
}

.btn-success {
    background: #67c23a;
    color: white;
    border-color: #67c23a;
}

.btn-success:hover {
    background: #85ce61;
    border-color: #85ce61;
}

.btn-default {
    background: #fff;
    color: #606266;
    border-color: #dcdfe6;
}

.btn-default:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background: #ecf5ff;
}

.btn-warning {
    background: #e6a23c;
    color: white;
    border-color: #e6a23c;
}

.btn-warning:hover {
    background: #ebb563;
    border-color: #ebb563;
}

/* 表格样式更新 */
.table-container {
    background: white;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    overflow: hidden;
}

.data-table th {
    background: #fafafa;
    color: #909399;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 1px solid #ebeef5;
}

.data-table td {
    font-size: 14px;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
}

.data-table tr:hover {
    background: #f5f7fa;
}

/* 分页样式 */
.pagination {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafafa;
    border-top: 1px solid #ebeef5;
}

.pagination-info {
    font-size: 14px;
    color: #909399;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-size {
    font-size: 14px;
    color: #606266;
}

.page-btn {
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    background: white;
    color: #606266;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.page-btn:hover {
    color: #409eff;
    border-color: #409eff;
}
