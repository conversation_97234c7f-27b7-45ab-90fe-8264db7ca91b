/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 登录页面样式 */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-box {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.login-header p {
    color: #666;
    margin-bottom: 30px;
}

.login-form {
    margin-bottom: 30px;
}

.form-group {
    position: relative;
    margin-bottom: 20px;
}

.form-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.form-group input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s;
}

.login-btn:hover {
    transform: translateY(-2px);
}

.demo-accounts {
    text-align: left;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.demo-accounts p {
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.demo-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    background: white;
    border-radius: 8px;
}

.demo-btn {
    padding: 5px 15px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
}

/* 主界面样式 */
.main-container {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-rows: 70px 1fr;
    grid-template-columns: 250px 1fr;
    height: 100vh;
    background: #f5f6fa;
}

/* 顶部导航 */
.header {
    grid-area: header;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-left i {
    font-size: 1.5rem;
    color: #667eea;
}

.header-left h1 {
    font-size: 1.3rem;
    color: #333;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
}

.logout-btn {
    padding: 8px 16px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s;
}

.logout-btn:hover {
    background: #c0392b;
}

/* 侧边栏 */
.sidebar {
    grid-area: sidebar;
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.nav-menu {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s;
    border-left: 4px solid transparent;
}

.nav-item:hover {
    background: #f8f9fa;
    border-left-color: #667eea;
}

.nav-item.active {
    background: #667eea;
    color: white;
    border-left-color: #5a67d8;
}

.nav-item i {
    font-size: 1.1rem;
    width: 20px;
}

/* 主内容区 */
.main-content {
    grid-area: main;
    padding: 30px;
    overflow-y: auto;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h2 {
    color: #333;
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-header p {
    color: #666;
    margin-top: 5px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-info h3 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e1e5e9;
}

.data-table td {
    padding: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status.active {
    background: #d4edda;
    color: #155724;
}

.status.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* 图表容器 */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #333;
}

.chart-placeholder {
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    background: #f8f9fa;
    border-radius: 10px;
}

.chart-placeholder i {
    font-size: 3rem;
    margin-bottom: 10px;
}

/* 消息提示 */
#messageContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.message {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #27ae60;
}

.message.error {
    background: #e74c3c;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        grid-template-areas:
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 70px 1fr;
    }

    .sidebar {
        display: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
