@echo off
echo ========================================
echo Java环境自动安装脚本
echo ========================================

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，继续安装...
) else (
    echo 错误：需要管理员权限运行此脚本
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 正在检查是否已安装Java...
java -version >nul 2>&1
if %errorLevel% == 0 (
    echo Java已安装，版本信息：
    java -version
    echo.
    set /p choice="是否要重新安装Java？(y/n): "
    if /i "%choice%" neq "y" goto check_maven
)

echo.
echo 正在检查Chocolatey...
choco -v >nul 2>&1
if %errorLevel% neq 0 (
    echo Chocolatey未安装，正在安装...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    
    if %errorLevel% neq 0 (
        echo Chocolatey安装失败，请检查网络连接或手动安装
        goto manual_install
    )
    
    echo Chocolatey安装成功！
    echo 刷新环境变量...
    call refreshenv
) else (
    echo Chocolatey已安装
)

echo.
echo 正在安装OpenJDK 8...
choco install openjdk8 -y

if %errorLevel% neq 0 (
    echo OpenJDK安装失败，尝试安装Oracle JDK...
    choco install jdk8 -y
    
    if %errorLevel% neq 0 (
        echo 自动安装失败，请手动安装
        goto manual_install
    )
)

echo Java安装完成！

:check_maven
echo.
echo 正在检查Maven...
mvn -v >nul 2>&1
if %errorLevel% == 0 (
    echo Maven已安装，版本信息：
    mvn -v
    goto verify
)

echo.
echo 正在安装Maven...
choco install maven -y

if %errorLevel% neq 0 (
    echo Maven安装失败
    goto manual_install
)

echo Maven安装完成！

:verify
echo.
echo ========================================
echo 验证安装结果
echo ========================================

echo 刷新环境变量...
call refreshenv

echo.
echo Java版本：
java -version
if %errorLevel% neq 0 (
    echo Java验证失败
    goto manual_install
)

echo.
echo Java编译器版本：
javac -version
if %errorLevel% neq 0 (
    echo Java编译器验证失败
    goto manual_install
)

echo.
echo Maven版本：
mvn -v
if %errorLevel% neq 0 (
    echo Maven验证失败
    goto manual_install
)

echo.
echo ========================================
echo 安装成功！
echo ========================================
echo 所有环境已正确安装和配置
echo 您现在可以运行Spring Boot项目了
echo.
echo 使用以下命令启动项目：
echo   mvn spring-boot:run
echo.
pause
exit /b 0

:manual_install
echo.
echo ========================================
echo 自动安装失败
echo ========================================
echo 请参考"Java环境安装指南.md"进行手动安装
echo.
echo 手动安装步骤：
echo 1. 访问 https://adoptium.net/temurin/releases/?version=8
echo 2. 下载Windows x64版本的.msi安装包
echo 3. 运行安装程序
echo 4. 配置环境变量JAVA_HOME和PATH
echo 5. 下载并安装Maven
echo.
pause
exit /b 1
