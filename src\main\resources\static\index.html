<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尚品甄选后台管理系统</title>
    <link rel="stylesheet" href="css/style.css?v=1.0">
    <link rel="stylesheet" href="css/vue-admin-style.css?v=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div id="loginPage" class="login-container">
            <div class="login-wrapper">
                <div class="login-box">
                    <div class="login-header">
                        <h1>尚品甄选后台管理系统</h1>
                    </div>
                    <form id="loginForm" class="login-form">
                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-user input-icon"></i>
                                <input type="text" id="username" placeholder="admin" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="password" placeholder="••••••" required>
                                <i class="fas fa-eye-slash password-toggle" onclick="togglePassword()"></i>
                            </div>
                        </div>
                        <div class="captcha-group">
                            <div class="input-wrapper captcha-input">
                                <input type="text" id="captcha" placeholder="请输入验证码" required>
                            </div>
                            <div class="captcha-image" onclick="refreshCaptcha()">
                                <canvas id="captchaCanvas" width="120" height="40"></canvas>
                            </div>
                        </div>
                        <div class="form-options">
                            <label class="remember-me">
                                <input type="checkbox" id="rememberMe">
                                <span class="checkmark"></span>
                                记住密码
                            </label>
                        </div>
                        <button type="submit" class="login-btn">
                            登录
                        </button>
                    </form>
                    <div class="demo-accounts">
                        <p>演示账号：</p>
                        <div class="demo-item">
                            <span>管理员：admin / 123456</span>
                            <button onclick="fillLogin('admin', '123456')" class="demo-btn">快速填入</button>
                        </div>
                        <div class="demo-item">
                            <span>测试用户：test / 123456</span>
                            <button onclick="fillLogin('test', '123456')" class="demo-btn">快速填入</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主界面 -->
        <div id="mainPage" class="main-container" style="display: none;">
            <!-- 顶部导航 -->
            <header class="header">
                <div class="header-left">
                    <div class="logo-section">
                        <i class="fas fa-cube logo-icon"></i>
                        <span class="logo-text">Vue3 Element Admin</span>
                    </div>
                    <button class="menu-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
                <div class="header-center">
                    <nav class="breadcrumb">
                        <span class="breadcrumb-item">首页</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-item" id="currentPage">系统管理</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-item active" id="currentSubPage">角色管理</span>
                    </nav>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn" title="搜索">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="action-btn" title="全屏">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="action-btn" title="设置">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <div class="user-info">
                        <img id="userAvatar" src="" alt="头像" class="avatar">
                        <span id="userName">admin</span>
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                        <div class="user-dropdown">
                            <div class="dropdown-item">
                                <i class="fas fa-user"></i>
                                个人中心
                            </div>
                            <div class="dropdown-item">
                                <i class="fas fa-cog"></i>
                                设置
                            </div>
                            <div class="dropdown-divider"></div>
                            <div class="dropdown-item" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 侧边栏 -->
            <aside class="sidebar">
                <nav class="nav-menu">
                    <div class="nav-item active" data-page="dashboard">
                        <i class="fas fa-home"></i>
                        <span>工作台</span>
                    </div>
                    <div class="nav-group">
                        <div class="nav-group-title">
                            <i class="fas fa-cog"></i>
                            <span>系统管理</span>
                            <i class="fas fa-chevron-down group-arrow"></i>
                        </div>
                        <div class="nav-group-items">
                            <div class="nav-item" data-page="roles">
                                <span class="nav-dot"></span>
                                <span>角色管理</span>
                            </div>
                            <div class="nav-item" data-page="users">
                                <span class="nav-dot"></span>
                                <span>用户管理</span>
                            </div>
                            <div class="nav-item" data-page="permissions">
                                <span class="nav-dot"></span>
                                <span>权限管理</span>
                            </div>
                        </div>
                    </div>
                    <div class="nav-item" data-page="products">
                        <i class="fas fa-box"></i>
                        <span>商品管理</span>
                    </div>
                    <div class="nav-item" data-page="categories">
                        <i class="fas fa-tags"></i>
                        <span>分类管理</span>
                    </div>
                    <div class="nav-item" data-page="orders">
                        <i class="fas fa-shopping-bag"></i>
                        <span>订单管理</span>
                    </div>
                </nav>
            </aside>

            <!-- 主内容区 -->
            <main class="main-content">
                <!-- 仪表盘页面 -->
                <div id="dashboardPage" class="page active">
                    <div class="page-header">
                        <h2><i class="fas fa-tachometer-alt"></i> 仪表盘</h2>
                        <p>系统概览和统计信息</p>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="stat-info">
                                <h3>商品总数</h3>
                                <p class="stat-number">1,234</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="stat-info">
                                <h3>订单总数</h3>
                                <p class="stat-number">5,678</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3>用户总数</h3>
                                <p class="stat-number">9,012</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-info">
                                <h3>今日销售额</h3>
                                <p class="stat-number">¥12,345</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-content">
                        <div class="chart-container">
                            <h3>销售趋势</h3>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line"></i>
                                <p>图表数据加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品管理页面 -->
                <div id="productsPage" class="page">
                    <div class="page-header">
                        <h2><i class="fas fa-box"></i> 商品管理</h2>
                        <button class="btn btn-primary" onclick="showAddProductModal()">
                            <i class="fas fa-plus"></i> 添加商品
                        </button>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>商品名称</th>
                                    <th>分类</th>
                                    <th>价格</th>
                                    <th>库存</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <tr>
                                    <td>1</td>
                                    <td>iPhone 15 Pro</td>
                                    <td>数码产品</td>
                                    <td>¥8,999</td>
                                    <td>50</td>
                                    <td><span class="status active">在售</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>MacBook Pro</td>
                                    <td>数码产品</td>
                                    <td>¥12,999</td>
                                    <td>30</td>
                                    <td><span class="status active">在售</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 角色管理页面 -->
                <div id="rolesPage" class="page">
                    <div class="page-header">
                        <div class="page-title">
                            <h2>角色管理</h2>
                        </div>
                        <div class="page-actions">
                            <div class="search-box">
                                <input type="text" placeholder="角色名称" class="search-input">
                                <input type="text" placeholder="角色Code" class="search-input">
                                <button class="btn btn-primary search-btn">搜索</button>
                            </div>
                            <button class="btn btn-success add-btn">
                                <i class="fas fa-plus"></i>
                                添加
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>角色名称</th>
                                    <th>角色Code</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>测试人员</td>
                                    <td>test</td>
                                    <td>2023-09-03 15:24:26</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">修改</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                        <button class="btn btn-sm btn-warning">分配权限</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>销售人员</td>
                                    <td>销售</td>
                                    <td>2023-09-03 15:23:04</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">修改</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                        <button class="btn btn-sm btn-warning">分配权限</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>用户管理员</td>
                                    <td>yhgly</td>
                                    <td>2023-05-04 02:36:22</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">修改</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                        <button class="btn btn-sm btn-warning">分配权限</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>平台管理员</td>
                                    <td>ptgly</td>
                                    <td>2023-05-04 02:36:06</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">修改</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                        <button class="btn btn-sm btn-warning">分配权限</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>test02</td>
                                    <td>test02</td>
                                    <td>2023-09-03 13:23:41</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">修改</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                        <button class="btn btn-sm btn-warning">分配权限</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="pagination">
                            <span class="pagination-info">共 5 条</span>
                            <div class="pagination-controls">
                                <span class="page-size">10条/页</span>
                                <button class="page-btn">1</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他页面占位符 -->
                <div id="categoriesPage" class="page">
                    <div class="page-header">
                        <h2><i class="fas fa-tags"></i> 分类管理</h2>
                    </div>
                    <p>分类管理功能开发中...</p>
                </div>

                <div id="ordersPage" class="page">
                    <div class="page-header">
                        <h2><i class="fas fa-shopping-bag"></i> 订单管理</h2>
                    </div>
                    <p>订单管理功能开发中...</p>
                </div>

                <div id="usersPage" class="page">
                    <div class="page-header">
                        <h2><i class="fas fa-users"></i> 用户管理</h2>
                    </div>
                    <p>用户管理功能开发中...</p>
                </div>

                <div id="settingsPage" class="page">
                    <div class="page-header">
                        <h2><i class="fas fa-cog"></i> 系统设置</h2>
                    </div>
                    <p>系统设置功能开发中...</p>
                </div>
            </main>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer"></div>

    <script src="js/app.js"></script>
</body>
</html>
