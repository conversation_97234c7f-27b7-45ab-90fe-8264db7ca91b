# API测试文档

## 测试环境

- **服务地址**: http://localhost:8080
- **默认用户**: admin/123456

## 接口测试

### 1. 健康检查

**请求**:
```http
GET http://localhost:8080/api/health
```

**预期响应**:
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "UP",
    "timestamp": "2023-12-07T10:30:00",
    "service": "spzx-admin",
    "version": "1.0.0"
  }
}
```

### 2. 用户登录

**请求**:
```http
POST http://localhost:8080/api/login
Content-Type: application/json

{
  "userName": "admin",
  "password": "123456"
}
```

**预期响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9.***************************************************************************************.xxx",
    "refresh_token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************.xxx"
  }
}
```

### 3. 获取用户信息

**请求**:
```http
GET http://localhost:8080/api/userinfo
Authorization: Bearer {token}
```

**预期响应**:
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "name": "管理员",
    "role": "admin",
    "avatar": "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
  }
}
```

### 4. 刷新Token

**请求**:
```http
PUT http://localhost:8080/api/authorizations
Authorization: Bearer {refresh_token}
```

**预期响应**:
```json
{
  "code": 200,
  "message": "token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9.xxx",
    "refresh_token": "eyJhbGciOiJIUzUxMiJ9.xxx"
  }
}
```

## 错误场景测试

### 1. 登录失败 - 用户名错误

**请求**:
```http
POST http://localhost:8080/api/login
Content-Type: application/json

{
  "userName": "wronguser",
  "password": "123456"
}
```

**预期响应**:
```json
{
  "code": 500,
  "message": "用户名或密码错误",
  "data": null
}
```

### 2. 登录失败 - 密码错误

**请求**:
```http
POST http://localhost:8080/api/login
Content-Type: application/json

{
  "userName": "admin",
  "password": "wrongpassword"
}
```

**预期响应**:
```json
{
  "code": 500,
  "message": "用户名或密码错误",
  "data": null
}
```

### 3. 未授权访问

**请求**:
```http
GET http://localhost:8080/api/userinfo
```

**预期响应**:
```json
{
  "code": 401,
  "message": "未授权访问",
  "data": null
}
```

### 4. Token无效

**请求**:
```http
GET http://localhost:8080/api/userinfo
Authorization: Bearer invalid_token
```

**预期响应**:
```json
{
  "code": 401,
  "message": "token无效",
  "data": null
}
```

## 使用Postman测试

### 1. 导入环境变量

创建环境变量：
- `baseUrl`: http://localhost:8080
- `token`: (登录后获取)
- `refreshToken`: (登录后获取)

### 2. 测试步骤

1. **健康检查**: GET {{baseUrl}}/api/health
2. **用户登录**: POST {{baseUrl}}/api/login
3. **保存token**: 从登录响应中复制token到环境变量
4. **获取用户信息**: GET {{baseUrl}}/api/userinfo (使用Bearer token)
5. **刷新token**: PUT {{baseUrl}}/api/authorizations (使用refresh token)

## 使用curl测试

### 1. 健康检查
```bash
curl -X GET http://localhost:8080/api/health
```

### 2. 用户登录
```bash
curl -X POST http://localhost:8080/api/login \
  -H "Content-Type: application/json" \
  -d '{"userName":"admin","password":"123456"}'
```

### 3. 获取用户信息
```bash
curl -X GET http://localhost:8080/api/userinfo \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 4. 刷新Token
```bash
curl -X PUT http://localhost:8080/api/authorizations \
  -H "Authorization: Bearer YOUR_REFRESH_TOKEN_HERE"
```

## 前端集成测试

### 修改前端配置

在前端项目的 `vite.config.js` 中，将代理目标修改为后端地址：

```javascript
server: {
  port: 3001,
  open: true,
  proxy: {
    '/api': {
      target: 'http://localhost:8080', // 后端地址
      changeOrigin: true,
      // rewrite: path => path.replace(/^\/api/, ''), // 注释掉这行
    },
  },
},
```

### 测试流程

1. 启动后端服务 (端口8080)
2. 启动前端服务 (端口3001)
3. 访问 http://localhost:3001
4. 使用 admin/123456 登录
5. 验证登录成功并能获取用户信息

## 注意事项

1. 确保后端服务已启动并运行在8080端口
2. Token有效期为24小时，refresh token有效期为7天
3. 所有API都支持跨域访问
4. 开发环境使用H2内存数据库，重启后数据会重置
