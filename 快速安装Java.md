# 快速安装Java环境

## 当前状态
❌ Java未安装 - 需要安装Java环境才能运行Spring Boot项目

## 推荐安装方式

### 方式一：自动安装（推荐）

1. **以管理员身份运行PowerShell**
   - 按 `Win + X`，选择"Windows PowerShell (管理员)"
   - 或者右键开始菜单，选择"Windows Terminal (管理员)"

2. **运行自动安装脚本**
   ```powershell
   Set-ExecutionPolicy Bypass -Scope Process -Force
   .\install-java-simple.ps1
   ```

### 方式二：手动下载安装

1. **下载OpenJDK 8**
   - 访问：https://adoptium.net/temurin/releases/?version=8
   - 选择：Windows x64 的 .msi 安装包
   - 文件名类似：OpenJDK8U-jdk_x64_windows_hotspot_8u392b08.msi

2. **安装JDK**
   - 双击下载的.msi文件
   - 按照安装向导完成安装
   - 默认安装路径：`C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot\`

3. **配置环境变量**
   
   **方法A：通过系统设置**
   - 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
   - 新建系统变量：
     - 变量名：`JAVA_HOME`
     - 变量值：`C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot`
   - 编辑系统变量`Path`，添加：`%JAVA_HOME%\bin`

   **方法B：通过PowerShell（管理员）**
   ```powershell
   # 设置JAVA_HOME（替换为实际安装路径）
   [Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot", "Machine")
   
   # 添加到PATH
   $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
   [Environment]::SetEnvironmentVariable("PATH", "$currentPath;%JAVA_HOME%\bin", "Machine")
   ```

4. **验证安装**
   - 重新打开命令提示符或PowerShell
   - 运行：`java -version`
   - 应该显示Java版本信息

### 方式三：使用Chocolatey包管理器

1. **安装Chocolatey**（以管理员身份运行PowerShell）
   ```powershell
   Set-ExecutionPolicy Bypass -Scope Process -Force
   [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
   iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
   ```

2. **安装Java**
   ```powershell
   choco install openjdk8 -y
   ```

3. **安装Maven**
   ```powershell
   choco install maven -y
   ```

## 安装Maven

如果只安装了Java，还需要安装Maven：

### 使用Chocolatey
```powershell
choco install maven -y
```

### 手动安装
1. 下载Maven：https://maven.apache.org/download.cgi
2. 解压到：`C:\Program Files\Apache\maven`
3. 设置环境变量：
   - `MAVEN_HOME`: `C:\Program Files\Apache\maven`
   - 在`Path`中添加：`%MAVEN_HOME%\bin`

## 验证完整环境

安装完成后，在新的命令提示符中运行：

```cmd
java -version
javac -version
mvn -version
```

如果都能正常显示版本信息，说明环境配置成功！

## 启动项目

环境配置完成后，可以使用以下命令启动Spring Boot项目：

```cmd
# 编译项目
mvn clean compile

# 启动项目
mvn spring-boot:run
```

或者直接运行：
```cmd
start.bat
```

## 常见问题

### 1. 'java' 不是内部或外部命令
- 检查JAVA_HOME是否设置正确
- 检查PATH中是否包含%JAVA_HOME%\bin
- 重启命令提示符

### 2. 权限不足
- 以管理员身份运行PowerShell或命令提示符

### 3. 网络问题
- 如果无法下载，可以使用手机热点或更换网络

## 需要帮助？

如果遇到问题，请：
1. 检查网络连接
2. 确保以管理员身份运行
3. 参考详细的"Java环境安装指南.md"
4. 或使用手动安装方式
