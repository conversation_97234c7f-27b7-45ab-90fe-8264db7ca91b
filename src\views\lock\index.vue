<!--
 * 
 * 　　┏┓　　　┏┓+ +
 * 　┏┛┻━━━┛┻┓ + +
 * 　┃　　　　　　　┃ 　
 * 　┃　　　━　　　┃ ++ + + +
 *  ████━████ ┃+
 * 　┃　　　　　　　┃ +
 * 　┃　　　┻　　　┃
 * 　┃　　　　　　　┃ + +
 * 　┗━┓　　　┏━┛
 * 　　　┃　　　┃　　　　　　　　　　　
 * 　　　┃　　　┃ + + + +
 * 　　　┃　　　┃
 * 　　　┃　　　┃ +  神兽保佑
 * 　　　┃　　　┃    代码无bug　　
 * 　　　┃　　　┃　　+　　　　　　　　　
 * 　　　┃　 　　┗━━━┓ + +
 * 　　　┃ 　　　　　　　┣┓
 * 　　　┃ 　　　　　　　┏┛
 * 　　　┗┓┓┏━┳┓┏┛ + + + +
 * 　　　　┃┫┫　┃┫┫
 * 　　　　┗┻┛　┗┻┛+ + + +
 * 
 * 
 * @Descripttion: 
 * @version: 
 * @Date: 2021-04-23 15:25:51
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-04-26 11:34:26
 * @Author: <EMAIL>
 * @HomePage: https://huzhushan.gitee.io/vue3-element-admin
 * @Github: https://github.com/huzhushan/vue3-element-admin
 * @Donate: https://huzhushan.gitee.io/vue3-element-admin/donate/
 -->

<template>
  <div class="lock-wrap">
    <Unlock />
    <Clock />
    <current-time />
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import Unlock from './Unlock.vue'
import Clock from './Clock.vue'
import CurrentTime from './CurrentTime.vue'

export default defineComponent({
  name: 'lock',
  components: {
    Unlock,
    Clock,
    CurrentTime,
  },
  setup() {},
})
</script>

<style lang="scss" scoped>
.lock-wrap {
  background: #222;
  height: 100%;
  min-height: 480px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>
