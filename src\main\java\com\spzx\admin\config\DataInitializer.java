package com.spzx.admin.config;

import com.spzx.admin.entity.User;
import com.spzx.admin.entity.Product;
import com.spzx.admin.repository.UserRepository;
import com.spzx.admin.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 数据初始化器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final ProductRepository productRepository;

    @Override
    public void run(String... args) throws Exception {
        initDefaultUser();
        initDefaultProducts();
    }

    /**
     * 初始化默认用户
     */
    private void initDefaultUser() {
        // 检查是否已存在admin用户
        if (userRepository.findByUserName("admin").isPresent()) {
            log.info("默认用户已存在，跳过初始化");
            return;
        }

        // 创建默认admin用户
        User adminUser = new User();
        adminUser.setUserName("admin");
        adminUser.setPassword("123456"); // 实际应用中应该加密
        adminUser.setName("管理员");
        adminUser.setRole("admin");
        adminUser.setStatus(1);
        adminUser.setAvatar("https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png");
        adminUser.setCreateTime(LocalDateTime.now());
        adminUser.setUpdateTime(LocalDateTime.now());

        userRepository.save(adminUser);
        log.info("默认用户初始化完成：admin/123456");

        // 创建测试用户
        User testUser = new User();
        testUser.setUserName("test");
        testUser.setPassword("123456");
        testUser.setName("测试用户");
        testUser.setRole("visitor");
        testUser.setStatus(1);
        testUser.setAvatar("https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png");
        testUser.setCreateTime(LocalDateTime.now());
        testUser.setUpdateTime(LocalDateTime.now());

        userRepository.save(testUser);
        log.info("测试用户初始化完成：test/123456");
    }

    /**
     * 初始化默认商品
     */
    private void initDefaultProducts() {
        // 检查是否已存在商品
        if (productRepository.count() > 0) {
            log.info("商品数据已存在，跳过初始化");
            return;
        }

        // 创建示例商品
        Product[] products = {
            createProduct("iPhone 15 Pro", "苹果最新旗舰手机，搭载A17 Pro芯片", "数码产品",
                         new BigDecimal("8999.00"), 50,
                         "https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/iphone-15-pro-finish-select-202309-6-1inch-naturaltitanium?wid=5120&hei=2880&fmt=p-jpg&qlt=80&.v=1692895395658"),

            createProduct("MacBook Pro 14英寸", "专业级笔记本电脑，M3 Pro芯片", "数码产品",
                         new BigDecimal("12999.00"), 30,
                         "https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/mbp14-spacegray-select-202310?wid=904&hei=840&fmt=jpeg&qlt=90&.v=1697311054290"),

            createProduct("AirPods Pro", "主动降噪无线耳机", "数码产品",
                         new BigDecimal("1899.00"), 100,
                         "https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/MQD83?wid=1144&hei=1144&fmt=jpeg&qlt=90&.v=1660803972361"),

            createProduct("小米13 Ultra", "徕卡影像旗舰手机", "数码产品",
                         new BigDecimal("5999.00"), 80,
                         "https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/7b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b.jpg"),

            createProduct("华为Mate 60 Pro", "卫星通信智能手机", "数码产品",
                         new BigDecimal("6999.00"), 60,
                         "https://consumer.huawei.com/content/dam/huawei-cbg-site/common/mkt/pdp/phones/mate60-pro/images/mate60-pro-kv.jpg"),

            createProduct("戴尔XPS 13", "轻薄商务笔记本", "数码产品",
                         new BigDecimal("7999.00"), 25,
                         "https://i.dell.com/is/image/DellContent/content/dam/ss2/product-images/dell-client-products/notebooks/xps-notebooks/xps-13-9315/media-gallery/notebook-xps-13-9315-nt-blue-gallery-1.psd?fmt=pjpg&pscan=auto&scl=1&wid=3491&hei=2137&qlt=100,1&resMode=sharp2&size=3491,2137&chrss=full&imwidth=5000"),

            createProduct("索尼WH-1000XM5", "无线降噪耳机", "数码产品",
                         new BigDecimal("2399.00"), 40,
                         "https://www.sony.com.cn/image/5d02da5df552836db894c83e2a5c9b0e?fmt=pjpg&wid=330&bgcolor=FFFFFF&bgc=FFFFFF"),

            createProduct("Nintendo Switch", "便携式游戏机", "游戏设备",
                         new BigDecimal("2099.00"), 35,
                         "https://assets.nintendo.com/image/upload/c_fill,w_1200/q_auto:best/f_auto/dpr_2.0/ncom/software/switch/70010000000025/7137262b5a64d921e193653f8aa0b722925abc5680380ca0e18a5cfd91697f58")
        };

        for (Product product : products) {
            productRepository.save(product);
        }

        log.info("默认商品数据初始化完成，共{}个商品", products.length);
    }

    private Product createProduct(String name, String description, String category,
                                 BigDecimal price, Integer stock, String imageUrl) {
        Product product = new Product();
        product.setName(name);
        product.setDescription(description);
        product.setCategory(category);
        product.setPrice(price);
        product.setStock(stock);
        product.setImageUrl(imageUrl);
        product.setStatus(1);
        product.setCreateTime(LocalDateTime.now());
        product.setUpdateTime(LocalDateTime.now());
        return product;
    }
}
