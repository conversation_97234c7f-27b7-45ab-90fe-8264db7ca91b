package com.spzx.admin.config;

import com.spzx.admin.entity.User;
import com.spzx.admin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 数据初始化器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {
    
    private final UserRepository userRepository;
    
    @Override
    public void run(String... args) throws Exception {
        initDefaultUser();
    }
    
    /**
     * 初始化默认用户
     */
    private void initDefaultUser() {
        // 检查是否已存在admin用户
        if (userRepository.findByUserName("admin").isPresent()) {
            log.info("默认用户已存在，跳过初始化");
            return;
        }
        
        // 创建默认admin用户
        User adminUser = new User();
        adminUser.setUserName("admin");
        adminUser.setPassword("123456"); // 实际应用中应该加密
        adminUser.setName("管理员");
        adminUser.setRole("admin");
        adminUser.setStatus(1);
        adminUser.setAvatar("https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png");
        adminUser.setCreateTime(LocalDateTime.now());
        adminUser.setUpdateTime(LocalDateTime.now());
        
        userRepository.save(adminUser);
        log.info("默认用户初始化完成：admin/123456");
        
        // 创建测试用户
        User testUser = new User();
        testUser.setUserName("test");
        testUser.setPassword("123456");
        testUser.setName("测试用户");
        testUser.setRole("visitor");
        testUser.setStatus(1);
        testUser.setAvatar("https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png");
        testUser.setCreateTime(LocalDateTime.now());
        testUser.setUpdateTime(LocalDateTime.now());
        
        userRepository.save(testUser);
        log.info("测试用户初始化完成：test/123456");
    }
}
