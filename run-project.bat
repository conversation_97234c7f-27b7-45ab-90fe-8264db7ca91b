@echo off
chcp 65001 >nul
echo ========================================
echo Spring Boot Project Launcher
echo ========================================

echo Setting up environment...
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot
set MAVEN_HOME=%CD%\apache-maven-3.9.6
set PATH=%MAVEN_HOME%\bin;%JAVA_HOME%\bin;%PATH%

echo Checking Java...
"%JAVA_HOME%\bin\java.exe" -version
if %errorLevel% neq 0 (
    echo Java check failed
    pause
    exit /b 1
)

echo.
echo Checking Maven...
"%MAVEN_HOME%\bin\mvn.cmd" -version
if %errorLevel% neq 0 (
    echo Maven check failed
    pause
    exit /b 1
)

echo.
echo Environment setup complete!
echo Java: %JAVA_HOME%
echo Maven: %MAVEN_HOME%

echo.
echo ========================================
echo Compiling Spring Boot Project
echo ========================================

echo Running: mvn clean compile
"%MAVEN_HOME%\bin\mvn.cmd" clean compile

if %errorLevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting Spring Boot Application
echo ========================================

echo Running: mvn spring-boot:run
echo.
echo The application will start on http://localhost:8080
echo Press Ctrl+C to stop the application
echo.

"%MAVEN_HOME%\bin\mvn.cmd" spring-boot:run

pause
