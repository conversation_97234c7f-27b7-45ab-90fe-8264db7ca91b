@echo off
chcp 65001 >nul
echo ========================================
echo Java环境安装脚本
echo ========================================
echo.

echo 正在检查管理员权限...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo.
    echo ❌ 需要管理员权限才能安装Java
    echo.
    echo 请按以下步骤操作：
    echo 1. 右键点击此文件 ^(立即安装Java.bat^)
    echo 2. 选择 "以管理员身份运行"
    echo 3. 在弹出的UAC对话框中点击 "是"
    echo.
    pause
    exit /b 1
)

echo ✅ 已获得管理员权限
echo.

echo 正在检查Java是否已安装...
java -version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Java已安装
    java -version
    echo.
    set /p choice="是否要重新安装Java？(y/n): "
    if /i "%choice%" neq "y" goto check_maven
)

echo.
echo 正在检查Chocolatey包管理器...
choco -v >nul 2>&1
if %errorLevel% neq 0 (
    echo 📦 正在安装Chocolatey包管理器...
    powershell -NoProfile -ExecutionPolicy Bypass -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    
    if %errorLevel% neq 0 (
        echo ❌ Chocolatey安装失败
        goto manual_install
    )
    
    echo ✅ Chocolatey安装成功
    call refreshenv
) else (
    echo ✅ Chocolatey已安装
)

echo.
echo 📦 正在安装OpenJDK 8...
choco install openjdk8 -y --no-progress

if %errorLevel% neq 0 (
    echo ⚠️ OpenJDK安装失败，尝试安装Oracle JDK...
    choco install jdk8 -y --no-progress
    
    if %errorLevel% neq 0 (
        echo ❌ Java安装失败
        goto manual_install
    )
)

echo ✅ Java安装完成

:check_maven
echo.
echo 正在检查Maven...
mvn -v >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Maven已安装
    goto verify
)

echo 📦 正在安装Maven...
choco install maven -y --no-progress

if %errorLevel% neq 0 (
    echo ❌ Maven安装失败
    goto manual_install
)

echo ✅ Maven安装完成

:verify
echo.
echo ========================================
echo 🔍 验证安装结果
echo ========================================

call refreshenv

echo.
echo Java版本：
java -version
if %errorLevel% neq 0 (
    echo ❌ Java验证失败
    goto manual_install
)

echo.
echo Maven版本：
mvn -v
if %errorLevel% neq 0 (
    echo ❌ Maven验证失败
    goto manual_install
)

echo.
echo ========================================
echo 🎉 安装成功！
echo ========================================
echo ✅ Java和Maven已成功安装
echo ✅ 环境变量已配置
echo.
echo 🚀 现在可以启动Spring Boot项目了！
echo.
echo 使用以下命令：
echo   mvn clean compile    ^(编译项目^)
echo   mvn spring-boot:run  ^(启动项目^)
echo.
echo 或者直接运行: start.bat
echo.
pause
exit /b 0

:manual_install
echo.
echo ========================================
echo ❌ 自动安装失败
echo ========================================
echo.
echo 请手动安装Java：
echo 1. 访问: https://adoptium.net/temurin/releases/?version=8
echo 2. 下载Windows x64版本的.msi安装包
echo 3. 双击运行安装程序
echo 4. 按照向导完成安装
echo.
echo 安装完成后，重新运行此脚本验证环境
echo.
pause
exit /b 1
