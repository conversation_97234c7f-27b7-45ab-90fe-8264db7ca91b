# Java环境简化安装脚本
# 需要以管理员身份运行PowerShell

Write-Host "========================================" -ForegroundColor Green
Write-Host "Java环境自动安装脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误：需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查是否已安装Java
Write-Host "`n正在检查Java环境..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Java已安装：" -ForegroundColor Green
        Write-Host $javaVersion -ForegroundColor Cyan
        $reinstall = Read-Host "是否要重新安装Java？(y/n)"
        if ($reinstall -ne "y" -and $reinstall -ne "Y") {
            Write-Host "跳过Java安装" -ForegroundColor Yellow
            goto CheckMaven
        }
    }
} catch {
    Write-Host "Java未安装" -ForegroundColor Red
}

# 安装Chocolatey
Write-Host "`n正在检查Chocolatey..." -ForegroundColor Yellow
try {
    choco -v | Out-Null
    Write-Host "Chocolatey已安装" -ForegroundColor Green
} catch {
    Write-Host "正在安装Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    try {
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Host "Chocolatey安装成功！" -ForegroundColor Green
        # 刷新环境变量
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    } catch {
        Write-Host "Chocolatey安装失败，请检查网络连接" -ForegroundColor Red
        goto ManualInstall
    }
}

# 安装OpenJDK 8
Write-Host "`n正在安装OpenJDK 8..." -ForegroundColor Yellow
try {
    choco install openjdk8 -y
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OpenJDK 8安装成功！" -ForegroundColor Green
    } else {
        throw "OpenJDK安装失败"
    }
} catch {
    Write-Host "OpenJDK安装失败，尝试安装Oracle JDK..." -ForegroundColor Yellow
    try {
        choco install jdk8 -y
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Oracle JDK安装成功！" -ForegroundColor Green
        } else {
            throw "Oracle JDK安装失败"
        }
    } catch {
        Write-Host "Java安装失败" -ForegroundColor Red
        goto ManualInstall
    }
}

:CheckMaven
# 检查Maven
Write-Host "`n正在检查Maven..." -ForegroundColor Yellow
try {
    mvn -v | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven已安装" -ForegroundColor Green
        mvn -v
        goto Verify
    }
} catch {
    Write-Host "Maven未安装" -ForegroundColor Red
}

# 安装Maven
Write-Host "`n正在安装Maven..." -ForegroundColor Yellow
try {
    choco install maven -y
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven安装成功！" -ForegroundColor Green
    } else {
        throw "Maven安装失败"
    }
} catch {
    Write-Host "Maven安装失败" -ForegroundColor Red
    goto ManualInstall
}

:Verify
# 验证安装
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "验证安装结果" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 刷新环境变量
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

Write-Host "`nJava版本：" -ForegroundColor Yellow
try {
    java -version
    if ($LASTEXITCODE -ne 0) { throw "Java验证失败" }
} catch {
    Write-Host "Java验证失败" -ForegroundColor Red
    goto ManualInstall
}

Write-Host "`nJava编译器版本：" -ForegroundColor Yellow
try {
    javac -version
    if ($LASTEXITCODE -ne 0) { throw "Java编译器验证失败" }
} catch {
    Write-Host "Java编译器验证失败" -ForegroundColor Red
    goto ManualInstall
}

Write-Host "`nMaven版本：" -ForegroundColor Yellow
try {
    mvn -v
    if ($LASTEXITCODE -ne 0) { throw "Maven验证失败" }
} catch {
    Write-Host "Maven验证失败" -ForegroundColor Red
    goto ManualInstall
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "安装成功！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "所有环境已正确安装和配置" -ForegroundColor Cyan
Write-Host "您现在可以运行Spring Boot项目了" -ForegroundColor Cyan
Write-Host "`n使用以下命令启动项目：" -ForegroundColor Yellow
Write-Host "  mvn spring-boot:run" -ForegroundColor White
Write-Host "`n或者运行：start.bat" -ForegroundColor Yellow

Read-Host "`n按任意键退出"
exit 0

:ManualInstall
Write-Host "`n========================================" -ForegroundColor Red
Write-Host "自动安装失败" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host "请参考'Java环境安装指南.md'进行手动安装" -ForegroundColor Yellow
Write-Host "`n手动安装步骤：" -ForegroundColor Yellow
Write-Host "1. 访问 https://adoptium.net/temurin/releases/?version=8" -ForegroundColor White
Write-Host "2. 下载Windows x64版本的.msi安装包" -ForegroundColor White
Write-Host "3. 运行安装程序" -ForegroundColor White
Write-Host "4. 配置环境变量JAVA_HOME和PATH" -ForegroundColor White
Write-Host "5. 下载并安装Maven" -ForegroundColor White

Read-Host "`n按任意键退出"
exit 1
