server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: spzx-admin
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************
    username: root
    password: root
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        
  # H2数据库配置（开发测试用）
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # 开发环境配置
  profiles:
    active: dev

# JWT配置
jwt:
  secret: spzx-admin-secret-key-2023
  expiration: 86400000  # 24小时，单位毫秒
  refresh-expiration: 604800000  # 7天，单位毫秒

# 日志配置
logging:
  level:
    com.spzx.admin: debug
    org.springframework.web: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
