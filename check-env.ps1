# Environment Check Script
Write-Host "========================================" -ForegroundColor Green
Write-Host "Environment Check" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

$javaOk = $false
$mavenOk = $false

# Check Java
Write-Host "`nChecking Java..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Java is installed" -ForegroundColor Green
        Write-Host $javaVersion -ForegroundColor Cyan
        $javaOk = $true
    }
} catch {
    Write-Host "✗ Java is not installed" -ForegroundColor Red
}

# Check JAVA_HOME
Write-Host "`nChecking JAVA_HOME..." -ForegroundColor Yellow
if ($env:JAVA_HOME) {
    Write-Host "✓ JAVA_HOME is set: $env:JAVA_HOME" -ForegroundColor Green
    if (Test-Path "$env:JAVA_HOME\bin\java.exe") {
        Write-Host "✓ JAVA_HOME path is valid" -ForegroundColor Green
    } else {
        Write-Host "✗ JAVA_HOME path is invalid" -ForegroundColor Red
    }
} else {
    Write-Host "✗ JAVA_HOME is not set" -ForegroundColor Red
}

# Check Maven
Write-Host "`nChecking Maven..." -ForegroundColor Yellow
try {
    $mavenVersion = mvn -v 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Maven is installed" -ForegroundColor Green
        Write-Host $mavenVersion -ForegroundColor Cyan
        $mavenOk = $true
    }
} catch {
    Write-Host "✗ Maven is not installed" -ForegroundColor Red
}

# Check project files
Write-Host "`nChecking project files..." -ForegroundColor Yellow
if (Test-Path "pom.xml") {
    Write-Host "✓ Found pom.xml" -ForegroundColor Green
} else {
    Write-Host "✗ pom.xml not found" -ForegroundColor Red
}

if (Test-Path "src\main\java") {
    Write-Host "✓ Found Java source directory" -ForegroundColor Green
} else {
    Write-Host "✗ Java source directory not found" -ForegroundColor Red
}

# Summary
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

if ($javaOk -and $mavenOk) {
    Write-Host "✓ Environment is ready!" -ForegroundColor Green
    Write-Host "`nYou can now run:" -ForegroundColor Yellow
    Write-Host "  mvn clean compile" -ForegroundColor White
    Write-Host "  mvn spring-boot:run" -ForegroundColor White
} else {
    Write-Host "✗ Environment setup incomplete" -ForegroundColor Red
    Write-Host "`nTo install Java and Maven:" -ForegroundColor Yellow
    Write-Host "  Run install-java.bat as Administrator" -ForegroundColor White
    Write-Host "  Or follow Java环境安装指南.md" -ForegroundColor White
}

Read-Host "`nPress any key to continue"
