# 环境配置说明

## 必需环境

### 1. Java Development Kit (JDK)

**版本要求**: JDK 8 或更高版本

**下载地址**: 
- Oracle JDK: https://www.oracle.com/java/technologies/downloads/
- OpenJDK: https://adoptium.net/

**安装步骤**:
1. 下载对应操作系统的JDK安装包
2. 运行安装程序，按提示完成安装
3. 配置环境变量：
   - 新建系统变量 `JAVA_HOME`，值为JDK安装路径（如：`C:\Program Files\Java\jdk1.8.0_XXX`）
   - 在系统变量 `Path` 中添加 `%JAVA_HOME%\bin`

**验证安装**:
```bash
java -version
javac -version
```

### 2. Apache Maven

**版本要求**: Maven 3.6 或更高版本

**下载地址**: https://maven.apache.org/download.cgi

**安装步骤**:
1. 下载Maven二进制压缩包
2. 解压到指定目录（如：`C:\Program Files\Apache\maven`）
3. 配置环境变量：
   - 新建系统变量 `MAVEN_HOME`，值为Maven安装路径
   - 在系统变量 `Path` 中添加 `%MAVEN_HOME%\bin`

**验证安装**:
```bash
mvn -version
```

## 可选环境

### 3. MySQL数据库

**版本要求**: MySQL 8.0 或更高版本

**下载地址**: https://dev.mysql.com/downloads/mysql/

**说明**: 
- 项目默认使用H2内存数据库，可以直接运行
- 如需使用MySQL，请修改 `application.yml` 中的数据库配置

### 4. 开发工具

推荐使用以下IDE之一：
- **IntelliJ IDEA** (推荐): https://www.jetbrains.com/idea/
- **Eclipse**: https://www.eclipse.org/downloads/
- **Visual Studio Code**: https://code.visualstudio.com/

## 快速启动

### 方法一：使用启动脚本（Windows）
```bash
# 双击运行 start.bat 文件
start.bat
```

### 方法二：使用Maven命令
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 方法三：使用IDE
1. 导入Maven项目
2. 等待依赖下载完成
3. 运行 `SpzxAdminApplication.java` 主类

## 验证启动

启动成功后，访问以下地址验证：

1. **健康检查**: http://localhost:8080/api/health
2. **H2数据库控制台**: http://localhost:8080/h2-console
   - JDBC URL: `jdbc:h2:mem:testdb`
   - 用户名: `sa`
   - 密码: (留空)

## 常见问题

### 1. 端口占用
如果8080端口被占用，可以修改 `application.yml` 中的端口配置：
```yaml
server:
  port: 8081  # 修改为其他端口
```

### 2. 依赖下载失败
如果Maven依赖下载缓慢或失败，可以配置国内镜像：

在Maven安装目录的 `conf/settings.xml` 文件中添加：
```xml
<mirrors>
  <mirror>
    <id>aliyun</id>
    <mirrorOf>central</mirrorOf>
    <name>Aliyun Central</name>
    <url>https://maven.aliyun.com/repository/central</url>
  </mirror>
</mirrors>
```

### 3. 编码问题
确保IDE和系统编码设置为UTF-8。

## 联系支持

如果遇到其他问题，请检查：
1. Java和Maven环境变量是否正确配置
2. 网络连接是否正常
3. 防火墙是否阻止了相关端口
