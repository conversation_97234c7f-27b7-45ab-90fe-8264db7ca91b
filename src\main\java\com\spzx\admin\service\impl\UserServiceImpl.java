package com.spzx.admin.service.impl;

import com.spzx.admin.dto.LoginRequest;
import com.spzx.admin.dto.LoginResponse;
import com.spzx.admin.dto.UserInfoResponse;
import com.spzx.admin.entity.User;
import com.spzx.admin.repository.UserRepository;
import com.spzx.admin.service.UserService;
import com.spzx.admin.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    private final JwtUtils jwtUtils;
    
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        log.info("用户登录，用户名：{}", loginRequest.getUserName());
        
        // 查找用户
        Optional<User> userOptional = userRepository.findByUserNameAndStatus(loginRequest.getUserName(), 1);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        User user = userOptional.get();
        
        // 验证密码（这里简化处理，实际应该使用加密）
        if (!loginRequest.getPassword().equals(user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 生成token
        String token = jwtUtils.generateToken(user.getUserName(), user.getId());
        String refreshToken = jwtUtils.generateRefreshToken(user.getUserName(), user.getId());
        
        log.info("用户登录成功，用户名：{}", loginRequest.getUserName());
        return new LoginResponse(token, refreshToken);
    }
    
    @Override
    public UserInfoResponse getUserInfo(String userName) {
        log.info("获取用户信息，用户名：{}", userName);
        
        Optional<User> userOptional = userRepository.findByUserNameAndStatus(userName, 1);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOptional.get();
        return new UserInfoResponse(user.getId(), user.getName(), user.getRole(), user.getAvatar());
    }
    
    @Override
    public LoginResponse refreshToken(String refreshToken) {
        log.info("刷新token");
        
        try {
            // 验证refresh token
            if (jwtUtils.isTokenExpired(refreshToken)) {
                throw new RuntimeException("refresh token已过期");
            }
            
            String userName = jwtUtils.getUserNameFromToken(refreshToken);
            Long userId = jwtUtils.getUserIdFromToken(refreshToken);
            
            // 生成新的token
            String newToken = jwtUtils.generateToken(userName, userId);
            
            log.info("token刷新成功，用户名：{}", userName);
            return new LoginResponse(newToken, refreshToken);
        } catch (Exception e) {
            log.error("token刷新失败：{}", e.getMessage());
            throw new RuntimeException("token刷新失败");
        }
    }
}
