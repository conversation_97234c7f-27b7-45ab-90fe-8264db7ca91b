# 尚品甄选管理后台系统 - 后端 (SPZX Admin Backend)

## 项目简介

这是一个基于Spring Boot的管理后台系统后端项目，为前端Vue3 Element Admin提供API支持。

## 技术栈

- **框架**: Spring Boot 2.7.14
- **数据库**: MySQL 8.0 / H2 (开发测试)
- **ORM**: Spring Data JPA
- **认证**: JWT
- **构建工具**: Maven
- **Java版本**: JDK 8

## 功能特性

- ✅ 用户登录认证
- ✅ JWT Token生成和验证
- ✅ Token自动刷新
- ✅ 用户信息管理
- ✅ 跨域支持
- ✅ 全局异常处理
- ✅ 数据自动初始化

## 快速开始

### 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+ (可选，默认使用H2内存数据库)

### 运行步骤

1. **运行项目**
   ```bash
   mvn spring-boot:run
   ```
   
   或者
   ```bash
   mvn clean package
   java -jar target/spzx-admin-1.0.0.jar
   ```

2. **访问应用**
   - 应用地址: http://localhost:8080
   - 健康检查: http://localhost:8080/api/health
   - H2控制台: http://localhost:8080/h2-console (开发环境)

## API接口

### 认证相关

#### 1. 用户登录
- **URL**: `POST /api/login`
- **请求体**:
  ```json
  {
    "userName": "admin",
    "password": "123456"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzUxMiJ9...",
      "refresh_token": "eyJhbGciOiJIUzUxMiJ9..."
    }
  }
  ```

#### 2. 获取用户信息
- **URL**: `GET /api/userinfo`
- **请求头**: `Authorization: Bearer <token>`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
      "id": 1,
      "name": "管理员",
      "role": "admin",
      "avatar": "https://..."
    }
  }
  ```

#### 3. 刷新Token
- **URL**: `PUT /api/authorizations`
- **请求头**: `Authorization: Bearer <refresh_token>`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "token刷新成功",
    "data": {
      "token": "eyJhbGciOiJIUzUxMiJ9...",
      "refresh_token": "eyJhbGciOiJIUzUxMiJ9..."
    }
  }
  ```

## 默认用户

系统启动时会自动创建以下测试用户：

| 用户名 | 密码   | 角色    | 说明     |
|--------|--------|---------|----------|
| admin  | 123456 | admin   | 管理员   |
| test   | 123456 | visitor | 测试用户 |

## 配置说明

### JWT配置
```yaml
jwt:
  secret: spzx-admin-secret-key-2023  # JWT密钥
  expiration: 86400000                # Token过期时间(24小时)
  refresh-expiration: 604800000       # Refresh Token过期时间(7天)
```

### 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************
    username: root
    password: root
  jpa:
    hibernate:
      ddl-auto: update  # 自动更新表结构
    show-sql: true      # 显示SQL语句
```

## 项目结构

```
src/
├── main/
│   ├── java/com/spzx/admin/
│   │   ├── SpzxAdminApplication.java     # 启动类
│   │   ├── common/                       # 通用类
│   │   │   └── Result.java              # 统一响应结果
│   │   ├── config/                       # 配置类
│   │   │   ├── CorsConfig.java          # 跨域配置
│   │   │   └── DataInitializer.java     # 数据初始化
│   │   ├── controller/                   # 控制器
│   │   │   ├── AuthController.java      # 认证控制器
│   │   │   └── TestController.java      # 测试控制器
│   │   ├── dto/                         # 数据传输对象
│   │   ├── entity/                      # 实体类
│   │   ├── exception/                   # 异常处理
│   │   ├── repository/                  # 数据访问层
│   │   ├── service/                     # 服务层
│   │   └── utils/                       # 工具类
│   └── resources/
│       └── application.yml              # 配置文件
```

## 许可证

MIT License
