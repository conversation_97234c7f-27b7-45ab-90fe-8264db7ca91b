<!--
 * @Descripttion: 
 * @version: 
 * @Date: 2021-04-20 11:06:21
 * @LastEditors: h<PERSON><PERSON><PERSON>@126.com
 * @LastEditTime: 2022-09-24 18:16:03
 * @Author: <EMAIL>
 * @HomePage: https://huzhushan.gitee.io/vue3-element-admin
 * @Github: https://github.com/huzhushan/vue3-element-admin
 * @Donate: https://huzhushan.gitee.io/vue3-element-admin/donate/
-->
<template>
  <h2>该页面入口不在菜单中显示</h2>
  <div>
    如果不需要在菜单中显示：
    <br />
    需要配置路由增加属性hidden: true，注意不是在meta中增加该属性，而是跟meta同级
  </div>
</template>
