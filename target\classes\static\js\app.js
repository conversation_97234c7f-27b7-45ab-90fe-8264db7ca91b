// 全局变量
let currentUser = null;
let authToken = null;

// API基础URL
const API_BASE = '/api';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 检查是否已登录
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');

    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        showMainPage();
    } else {
        showLoginPage();
    }

    // 绑定事件
    bindEvents();
}

// 绑定事件
function bindEvents() {
    // 登录表单提交
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // 退出登录
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    // 导航菜单点击
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const page = this.dataset.page;
            if (page) {
                showPage(page);
                setActiveNav(this);
            }
        });
    });
}

// 处理登录
async function handleLogin(event) {
    event.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!username || !password) {
        showMessage('请输入用户名和密码', 'error');
        return;
    }

    try {
        showMessage('正在登录...', 'info');

        const response = await fetch(`${API_BASE}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userName: username,
                password: password
            })
        });

        const result = await response.json();

        if (result.code === 200) {
            // 登录成功
            authToken = result.data.token;

            // 获取用户信息
            const userInfo = await getUserInfo();
            if (userInfo) {
                currentUser = userInfo;

                // 保存到本地存储
                localStorage.setItem('authToken', authToken);
                localStorage.setItem('currentUser', JSON.stringify(currentUser));

                showMessage('登录成功！', 'success');
                showMainPage();
            }
        } else {
            showMessage(result.message || '登录失败', 'error');
        }
    } catch (error) {
        console.error('登录错误:', error);
        showMessage('网络错误，请稍后重试', 'error');
    }
}

// 获取用户信息
async function getUserInfo() {
    try {
        const response = await fetch(`${API_BASE}/userinfo`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        const result = await response.json();

        if (result.code === 200) {
            return result.data;
        } else {
            showMessage('获取用户信息失败', 'error');
            return null;
        }
    } catch (error) {
        console.error('获取用户信息错误:', error);
        return null;
    }
}

// 处理退出登录
function handleLogout() {
    if (confirm('确定要退出登录吗？')) {
        // 清除本地存储
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');

        // 重置全局变量
        authToken = null;
        currentUser = null;

        showMessage('已退出登录', 'success');
        showLoginPage();
    }
}

// 显示登录页面
function showLoginPage() {
    document.getElementById('loginPage').style.display = 'flex';
    document.getElementById('mainPage').style.display = 'none';

    // 清空登录表单
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
}

// 显示主页面
function showMainPage() {
    document.getElementById('loginPage').style.display = 'none';
    document.getElementById('mainPage').style.display = 'block';

    // 更新用户信息显示
    if (currentUser) {
        document.getElementById('currentUser').textContent = currentUser.name || currentUser.userName || '用户';
    }

    // 默认显示仪表盘
    showPage('dashboard');
}

// 显示指定页面
function showPage(pageName) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });

    // 显示指定页面
    const targetPage = document.getElementById(pageName + 'Page');
    if (targetPage) {
        targetPage.classList.add('active');
    }

    // 根据页面加载数据
    loadPageData(pageName);
}

// 设置活动导航项
function setActiveNav(activeItem) {
    // 移除所有活动状态
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });

    // 设置当前项为活动状态
    activeItem.classList.add('active');
}

// 加载页面数据
function loadPageData(pageName) {
    switch (pageName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'products':
            loadProductsData();
            break;
        case 'categories':
            loadCategoriesData();
            break;
        case 'orders':
            loadOrdersData();
            break;
        case 'users':
            loadUsersData();
            break;
        case 'settings':
            loadSettingsData();
            break;
    }
}

// 加载仪表盘数据
function loadDashboardData() {
    // 这里可以调用API获取实际数据
    console.log('加载仪表盘数据');
}

// 加载商品数据
async function loadProductsData() {
    try {
        showMessage('正在加载商品数据...', 'info');

        const result = await apiRequest(`${API_BASE}/products`);
        if (result && result.code === 200) {
            renderProductsTable(result.data);
            showMessage('商品数据加载成功', 'success');
        } else {
            showMessage('加载商品数据失败', 'error');
        }
    } catch (error) {
        console.error('加载商品数据错误:', error);
        showMessage('加载商品数据失败', 'error');
    }
}

// 加载分类数据
function loadCategoriesData() {
    console.log('加载分类数据');
}

// 加载订单数据
function loadOrdersData() {
    console.log('加载订单数据');
}

// 加载用户数据
function loadUsersData() {
    console.log('加载用户数据');
}

// 加载设置数据
function loadSettingsData() {
    console.log('加载设置数据');
}

// 快速填入登录信息
function fillLogin(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;

    container.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// 渲染商品表格
function renderProductsTable(products) {
    const tbody = document.getElementById('productsTableBody');
    if (!tbody) return;

    if (!products || products.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #999;">暂无商品数据</td></tr>';
        return;
    }

    tbody.innerHTML = products.map(product => `
        <tr>
            <td>${product.id}</td>
            <td>${product.name}</td>
            <td>${product.category || '未分类'}</td>
            <td>¥${product.price}</td>
            <td>${product.stock}</td>
            <td><span class="status ${product.status === 1 ? 'active' : 'inactive'}">${product.statusText}</span></td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editProduct(${product.id})">编辑</button>
                <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})">删除</button>
            </td>
        </tr>
    `).join('');
}

// 显示添加商品模态框
function showAddProductModal() {
    showMessage('添加商品功能开发中...', 'info');
}

// 编辑商品
function editProduct(id) {
    showMessage(`编辑商品 ID: ${id} 功能开发中...`, 'info');
}

// 删除商品
async function deleteProduct(id) {
    if (!confirm('确定要删除这个商品吗？')) {
        return;
    }

    try {
        const result = await apiRequest(`${API_BASE}/products/${id}`, {
            method: 'DELETE'
        });

        if (result && result.code === 200) {
            showMessage('商品删除成功', 'success');
            loadProductsData(); // 重新加载商品列表
        } else {
            showMessage(result?.message || '删除商品失败', 'error');
        }
    } catch (error) {
        console.error('删除商品错误:', error);
        showMessage('删除商品失败', 'error');
    }
}

// API请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };

    if (authToken) {
        defaultOptions.headers['Authorization'] = `Bearer ${authToken}`;
    }

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(url, finalOptions);
        const result = await response.json();

        // 如果token过期，重新登录
        if (result.code === 401) {
            showMessage('登录已过期，请重新登录', 'error');
            handleLogout();
            return null;
        }

        return result;
    } catch (error) {
        console.error('API请求错误:', error);
        showMessage('网络错误，请稍后重试', 'error');
        return null;
    }
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');
}

// 格式化金额
function formatMoney(amount) {
    return '¥' + Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}
