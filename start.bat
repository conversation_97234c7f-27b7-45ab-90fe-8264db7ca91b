@echo off
echo ========================================
echo 尚品甄选管理后台系统 - 后端启动脚本
echo ========================================

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请先安装JDK 8或更高版本
    echo 下载地址：https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请先安装Maven
    echo 下载地址：https://maven.apache.org/download.cgi
    pause
    exit /b 1
)

echo 开始编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo 启动应用...
mvn spring-boot:run

pause
