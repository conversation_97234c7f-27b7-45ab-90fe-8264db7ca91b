# 🚀 下一步操作指南

## 当前状态
❌ **需要管理员权限安装Java**

## 立即行动步骤

### 🎯 推荐方法：使用安装脚本

1. **在文件资源管理器中**，找到项目目录：
   ```
   E:\专业综合项目\spzx\spzx-admin
   ```

2. **右键点击** `立即安装Java.bat` 文件

3. **选择** "以管理员身份运行"

4. **在弹出的UAC对话框中点击** "是"

5. **等待安装完成**（大约2-5分钟）

### 🔧 备选方法：手动安装

如果自动安装失败：

1. **下载Java**：
   - 访问：https://adoptium.net/temurin/releases/?version=8
   - 下载 Windows x64 的 .msi 安装包

2. **安装Java**：
   - 双击下载的.msi文件
   - 按照向导完成安装

3. **下载Maven**：
   - 访问：https://maven.apache.org/download.cgi
   - 下载二进制zip文件

4. **配置环境变量**：
   - 设置 JAVA_HOME
   - 设置 MAVEN_HOME
   - 更新 PATH

## 验证安装

安装完成后，运行验证脚本：
```
双击运行: 验证安装.bat
```

或在命令提示符中运行：
```cmd
java -version
mvn -version
```

## 启动项目

环境配置完成后，可以启动Spring Boot项目：

### 方法一：使用启动脚本
```
双击运行: start.bat
```

### 方法二：使用命令行
```cmd
# 编译项目
mvn clean compile

# 启动项目
mvn spring-boot:run
```

## 预期结果

项目启动成功后，您将看到：
- 控制台显示Spring Boot启动日志
- 最后显示："尚品甄选管理后台系统启动成功！"
- 服务运行在：http://localhost:8080

## 测试接口

项目启动后，可以测试以下接口：

1. **健康检查**：
   ```
   GET http://localhost:8080/api/health
   ```

2. **用户登录**：
   ```
   POST http://localhost:8080/api/login
   Content-Type: application/json
   
   {
     "userName": "admin",
     "password": "123456"
   }
   ```

## 与前端集成

后端启动成功后：

1. **修改前端配置**：
   - 在前端项目的 `vite.config.js` 中
   - 将代理目标设置为：`http://localhost:8080`

2. **启动前端项目**：
   ```bash
   npm run dev
   ```

3. **访问前端**：
   ```
   http://localhost:3001
   ```

4. **使用默认账户登录**：
   - 用户名：admin
   - 密码：123456

## 需要帮助？

如果遇到问题：

1. **查看错误信息**：注意控制台的错误提示
2. **检查端口占用**：确保8080端口未被占用
3. **查看日志**：Spring Boot会显示详细的启动日志
4. **参考文档**：查看项目中的其他说明文档

---

**现在请按照上述步骤安装Java环境，然后就可以运行Spring Boot项目了！** 🎉
