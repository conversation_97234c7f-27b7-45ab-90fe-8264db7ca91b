# Java环境安装指南

## 方法一：使用Chocolatey自动安装（推荐）

### 1. 安装Chocolatey包管理器

以管理员身份运行PowerShell，执行以下命令：

```powershell
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

### 2. 安装OpenJDK 8

```powershell
choco install openjdk8 -y
```

或者安装Oracle JDK 8：

```powershell
choco install jdk8 -y
```

### 3. 验证安装

```powershell
java -version
javac -version
```

## 方法二：手动下载安装

### 1. 下载JDK

访问以下网站下载JDK 8：

**OpenJDK（免费推荐）**：
- Eclipse Temurin: https://adoptium.net/temurin/releases/?version=8
- 选择Windows x64版本的.msi安装包

**Oracle JDK**：
- Oracle官网: https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html
- 需要注册Oracle账号

### 2. 安装步骤

1. 双击下载的.msi安装包
2. 按照安装向导进行安装
3. 建议安装到默认路径：`C:\Program Files\Java\jdk1.8.0_XXX`

### 3. 配置环境变量

#### 方法A：通过系统设置

1. 右键"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"中点击"新建"：
   - 变量名：`JAVA_HOME`
   - 变量值：`C:\Program Files\Java\jdk1.8.0_XXX`（替换为实际安装路径）
5. 找到"Path"变量，点击"编辑"
6. 点击"新建"，添加：`%JAVA_HOME%\bin`
7. 点击"确定"保存所有设置

#### 方法B：通过PowerShell命令

```powershell
# 设置JAVA_HOME（替换为实际安装路径）
[Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Java\jdk1.8.0_XXX", "Machine")

# 添加到PATH
$path = [Environment]::GetEnvironmentVariable("PATH", "Machine")
[Environment]::SetEnvironmentVariable("PATH", "$path;%JAVA_HOME%\bin", "Machine")
```

### 4. 验证安装

重新打开命令提示符或PowerShell，执行：

```cmd
java -version
javac -version
echo %JAVA_HOME%
```

## 方法三：使用便携版（无需管理员权限）

### 1. 下载便携版JDK

- 下载OpenJDK 8的zip压缩包
- 解压到任意目录，如：`D:\Java\jdk1.8.0_XXX`

### 2. 设置用户环境变量

1. Win+R 输入 `sysdm.cpl` 回车
2. 点击"环境变量"
3. 在"用户变量"中设置：
   - `JAVA_HOME`: `D:\Java\jdk1.8.0_XXX`
   - 在`Path`中添加：`%JAVA_HOME%\bin`

## 安装Maven

### 使用Chocolatey安装

```powershell
choco install maven -y
```

### 手动安装

1. 下载Maven：https://maven.apache.org/download.cgi
2. 解压到：`C:\Program Files\Apache\maven`
3. 设置环境变量：
   - `MAVEN_HOME`: `C:\Program Files\Apache\maven`
   - 在`Path`中添加：`%MAVEN_HOME%\bin`

## 验证完整环境

```cmd
java -version
javac -version
mvn -version
echo %JAVA_HOME%
echo %MAVEN_HOME%
```

## 常见问题解决

### 1. 'java' 不是内部或外部命令

- 检查JAVA_HOME是否设置正确
- 检查PATH中是否包含%JAVA_HOME%\bin
- 重启命令提示符或重启电脑

### 2. 权限不足

- 以管理员身份运行PowerShell
- 或使用便携版安装方式

### 3. 多个Java版本冲突

- 确保JAVA_HOME指向正确版本
- 检查PATH中Java路径的顺序

## 快速安装脚本

我已经为您准备了自动安装脚本，请查看项目根目录下的：
- `install-java.bat` - 自动安装脚本
- `check-environment.bat` - 环境检查脚本
