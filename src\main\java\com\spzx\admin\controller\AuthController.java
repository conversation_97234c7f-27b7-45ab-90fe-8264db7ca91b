package com.spzx.admin.controller;

import com.spzx.admin.common.Result;
import com.spzx.admin.dto.LoginRequest;
import com.spzx.admin.dto.LoginResponse;
import com.spzx.admin.dto.UserInfoResponse;
import com.spzx.admin.service.UserService;
import com.spzx.admin.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {
    
    private final UserService userService;
    private final JwtUtils jwtUtils;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            LoginResponse loginResponse = userService.login(loginRequest);
            return Result.success("登录成功", loginResponse);
        } catch (Exception e) {
            log.error("登录失败：{}", e.getMessage());
            return Result.error(400, e.getMessage());
        }
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/userinfo")
    public Result<UserInfoResponse> getUserInfo(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error(401, "未授权访问");
            }
            
            String userName = jwtUtils.getUserNameFromToken(token);
            if (!jwtUtils.validateToken(token, userName)) {
                return Result.error(401, "token无效");
            }
            
            UserInfoResponse userInfo = userService.getUserInfo(userName);
            return Result.success("获取用户信息成功", userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败：{}", e.getMessage());
            return Result.error(401, "获取用户信息失败");
        }
    }
    
    /**
     * 刷新token
     */
    @PutMapping("/authorizations")
    public Result<LoginResponse> refreshToken(HttpServletRequest request) {
        try {
            String refreshToken = getTokenFromRequest(request);
            if (refreshToken == null) {
                return Result.error(401, "未授权访问");
            }
            
            LoginResponse loginResponse = userService.refreshToken(refreshToken);
            return Result.success("token刷新成功", loginResponse);
        } catch (Exception e) {
            log.error("token刷新失败：{}", e.getMessage());
            return Result.error(401, e.getMessage());
        }
    }
    
    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
