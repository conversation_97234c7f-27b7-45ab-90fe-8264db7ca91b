@echo off
echo ========================================
echo 环境检查脚本
echo ========================================

set JAVA_OK=0
set MAVEN_OK=0
set ALL_OK=1

echo.
echo 检查Java环境...
echo ----------------------------------------

java -version >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Java已安装
    echo 版本信息：
    java -version
    set JAVA_OK=1
) else (
    echo ✗ Java未安装或未正确配置
    set ALL_OK=0
)

echo.
echo 检查Java编译器...
javac -version >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Java编译器可用
    javac -version
) else (
    echo ✗ Java编译器不可用
    set ALL_OK=0
)

echo.
echo 检查JAVA_HOME环境变量...
if defined JAVA_HOME (
    echo ✓ JAVA_HOME已设置: %JAVA_HOME%
    if exist "%JAVA_HOME%\bin\java.exe" (
        echo ✓ JAVA_HOME路径有效
    ) else (
        echo ✗ JAVA_HOME路径无效
        set ALL_OK=0
    )
) else (
    echo ✗ JAVA_HOME未设置
    set ALL_OK=0
)

echo.
echo 检查Maven环境...
echo ----------------------------------------

mvn -v >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Maven已安装
    echo 版本信息：
    mvn -v
    set MAVEN_OK=1
) else (
    echo ✗ Maven未安装或未正确配置
    set ALL_OK=0
)

echo.
echo 检查MAVEN_HOME环境变量...
if defined MAVEN_HOME (
    echo ✓ MAVEN_HOME已设置: %MAVEN_HOME%
    if exist "%MAVEN_HOME%\bin\mvn.cmd" (
        echo ✓ MAVEN_HOME路径有效
    ) else (
        echo ✗ MAVEN_HOME路径无效
        set ALL_OK=0
    )
) else (
    echo ! MAVEN_HOME未设置（可选，但建议设置）
)

echo.
echo 检查网络连接...
echo ----------------------------------------

ping -n 1 maven.apache.org >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 网络连接正常（可访问Maven仓库）
) else (
    echo ! 网络连接可能有问题（无法访问Maven仓库）
    echo   这可能影响依赖下载
)

echo.
echo 检查项目文件...
echo ----------------------------------------

if exist "pom.xml" (
    echo ✓ 找到pom.xml文件
) else (
    echo ✗ 未找到pom.xml文件
    echo   请确保在项目根目录运行此脚本
    set ALL_OK=0
)

if exist "src\main\java" (
    echo ✓ 找到Java源码目录
) else (
    echo ✗ 未找到Java源码目录
    set ALL_OK=0
)

echo.
echo ========================================
echo 检查结果汇总
echo ========================================

if %ALL_OK% == 1 (
    echo ✓ 所有环境检查通过！
    echo.
    echo 您可以使用以下命令：
    echo   编译项目: mvn clean compile
    echo   运行项目: mvn spring-boot:run
    echo   打包项目: mvn clean package
    echo.
    echo 要启动项目，请运行: start.bat
) else (
    echo ✗ 环境检查未通过
    echo.
    echo 需要解决的问题：
    if %JAVA_OK% == 0 (
        echo   - 安装和配置Java环境
    )
    if %MAVEN_OK% == 0 (
        echo   - 安装和配置Maven环境
    )
    echo.
    echo 解决方案：
    echo   1. 运行 install-java.bat 自动安装
    echo   2. 参考 Java环境安装指南.md 手动安装
)

echo.
pause
