@echo off
chcp 65001 >nul
echo ========================================
echo 验证Java和Maven安装
echo ========================================
echo.

echo 检查Java...
java -version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Java已安装
    echo 版本信息：
    java -version
) else (
    echo ❌ Java未安装或未正确配置
    echo.
    echo 请检查：
    echo 1. Java是否已安装
    echo 2. JAVA_HOME环境变量是否设置
    echo 3. PATH中是否包含%%JAVA_HOME%%\bin
)

echo.
echo 检查Java编译器...
javac -version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Java编译器可用
    javac -version
) else (
    echo ❌ Java编译器不可用
)

echo.
echo 检查Maven...
mvn -v >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Maven已安装
    echo 版本信息：
    mvn -v | findstr "Apache Maven"
) else (
    echo ❌ Maven未安装或未正确配置
    echo.
    echo 如果Java已安装，可以单独安装Maven：
    echo 1. 访问: https://maven.apache.org/download.cgi
    echo 2. 下载并解压Maven
    echo 3. 配置MAVEN_HOME和PATH环境变量
)

echo.
echo 检查项目文件...
if exist "pom.xml" (
    echo ✅ 找到pom.xml文件
) else (
    echo ❌ 未找到pom.xml文件
    echo 请确保在项目根目录运行此脚本
)

if exist "src\main\java" (
    echo ✅ 找到Java源码目录
) else (
    echo ❌ 未找到Java源码目录
)

echo.
echo ========================================
echo 总结
echo ========================================

java -version >nul 2>&1
set JAVA_OK=%errorLevel%

mvn -v >nul 2>&1
set MAVEN_OK=%errorLevel%

if %JAVA_OK% == 0 if %MAVEN_OK% == 0 (
    echo ✅ 环境配置完成！
    echo.
    echo 🚀 现在可以启动Spring Boot项目：
    echo   mvn clean compile    ^(编译项目^)
    echo   mvn spring-boot:run  ^(启动项目^)
    echo.
    echo 或者直接运行: start.bat
) else (
    echo ❌ 环境配置未完成
    echo.
    if %JAVA_OK% neq 0 (
        echo 需要安装Java
    )
    if %MAVEN_OK% neq 0 (
        echo 需要安装Maven
    )
    echo.
    echo 请运行安装脚本或手动安装
)

echo.
pause
