# API Test Script
Write-Host "========================================" -ForegroundColor Green
Write-Host "Testing Spring Boot APIs" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Test Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/health" -Method GET
    Write-Host "✓ Health Check Success!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $healthResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "✗ Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Login
Write-Host "`n2. Testing Login..." -ForegroundColor Yellow
$loginBody = @{
    userName = "admin"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "✓ Login Success!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $loginResponse | ConvertTo-Json -Depth 3
    
    # Save token for next test
    $token = $loginResponse.data.token
    Write-Host "`nToken saved for user info test" -ForegroundColor Yellow
    
    # Test User Info
    Write-Host "`n3. Testing User Info..." -ForegroundColor Yellow
    $headers = @{
        "Authorization" = "Bearer $token"
    }
    
    try {
        $userInfoResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/userinfo" -Method GET -Headers $headers
        Write-Host "✓ User Info Success!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor Cyan
        $userInfoResponse | ConvertTo-Json -Depth 3
    } catch {
        Write-Host "✗ User Info Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "✗ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "API Testing Complete" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Read-Host "`nPress any key to continue"
